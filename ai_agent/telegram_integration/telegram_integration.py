import sys
import os
import asyncio
import telethon
import frappe
from frappe.utils.file_manager import save_file
import json
import random
import logging
import mimetypes
import traceback
import uuid
from telethon import TelegramClient, events, types, functions
from telethon.errors import FileReferenceExpiredError, ChannelInvalidError, ChannelPrivateError, ChatWriteForbiddenError
from telethon.tl.types import (
	PeerUser, PeerChannel, MessageMediaPhoto, MessageMediaDocument,
	DocumentAttributeAudio, DocumentAttributeFilename, Document, DocumentEmpty
)
from datetime import datetime, timezone, timedelta
from typing import Any, Optional, Union
from http import HTTPStatus
import hmac
import hashlib
import sqlite3
import time

# --- DATA ROOT & BOT ROOT HELPERS ---
def resolve_data_root() -> str:
	"""
	Walk up from this file's directory to find a folder named 'TGCrewData'.
	If not found, create it next to the nearest parent and return that path.
	"""
	start_dir = os.path.abspath(os.path.dirname(__file__))
	current = start_dir
	while True:
		candidate = os.path.join(current, "TGCrewData")
		if os.path.isdir(candidate):
			return candidate
		parent = os.path.dirname(current)
		if parent == current:
			break
		current = parent
	# Fallback: create TGCrewData alongside this repository (two levels up from this file)
	fallback = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir)), "TGCrewData")
	os.makedirs(fallback, exist_ok=True)
	return fallback

def get_bot_root(agent_name: str) -> str:
	"""
	Returns a per-bot root inside TGCrewData and ensures it exists.
	Example: <.../TGCrewData/>/CupcakeCloud04
	"""
	root = os.path.join(resolve_data_root(), agent_name)
	os.makedirs(root, exist_ok=True)
	return root

DATA_ROOT = resolve_data_root()

logging.basicConfig(
	level=logging.INFO,
	format='%(asctime)s - %(levelname)s - %(message)s',
	handlers=[
		logging.StreamHandler(),
		logging.FileHandler('telegram_integration.log')
	]
)
logger = logging.getLogger(__name__)

# Шляжи до сайту та теки із сайтами
SITE_NAME = "32016-51127.bacloud.info"
# SITE_NAME = "ai-agent.localhost"
BENCH_PATH = "/home/<USER>/frappe-bench"
# BENCH_PATH = "/workspace/development/frappe-bench"
SITES_PATH = BENCH_PATH + "/sites"

bench_path = BENCH_PATH
apps_path = os.path.join(bench_path, "apps")
sys.path.extend([
	bench_path,
	apps_path,
	os.path.join(apps_path, "frappe"),
	os.path.join(apps_path, "ai_agent")
])

frappe.init(site=SITE_NAME, sites_path=SITES_PATH)
frappe.connect()

def get_site_name() -> str:
	return SITE_NAME

def get_site_path(site_name: str, path: str) -> str:
	return os.path.join(SITES_PATH, site_name, path)

BOT_IDS = set()
ACTIVE_CLIENTS = {}
BOT_CHAT_IDS = {}

def retry_db_operation(func, max_attempts=5, delay=2):
	async def async_wrapper(*args, **kwargs):
		for attempt in range(1, max_attempts + 1):
			try:
				return await func(*args, **kwargs)
			except sqlite3.OperationalError as e:
				if "database is locked" in str(e) and attempt < max_attempts:
					logger.warning(f"Database locked, retrying {attempt}/{max_attempts} after {delay}s")
					await asyncio.sleep(delay)
				else:
					raise

	def sync_wrapper(*args, **kwargs):
		for attempt in range(1, max_attempts + 1):
			try:
				return func(*args, **kwargs)
			except sqlite3.OperationalError as e:
				if "database is locked" in str(e) and attempt < max_attempts:
					logger.warning(f"Database locked, retrying {attempt}/{max_attempts} after {delay}s")
					time.sleep(delay)
				else:
					raise

	return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


@retry_db_operation
def upload_to_frappe(media_path: str, file_name: str, message_id: str) -> Optional[str]:
	sanitized_file_name = file_name.replace(" ", "_")
	unique_file_name = f"{message_id}_{sanitized_file_name}"
	try:
		with open(media_path, 'rb') as file_content:
			file_doc = save_file(unique_file_name, file_content.read(), "Message", message_id, is_private=0)
			file_url = file_doc.get("file_url")
			if isinstance(file_url, (list, tuple)):
				file_url = file_url[0] if file_url else None
			if file_url:
				logger.info(f"Файл збережено: file_name={file_doc.get('file_name')}, file_url={file_url}")
				return file_url
			logger.error(f"Не вдалося отримати file_url для message_id={message_id}")
			return None
	except Exception as e:
		logger.error(f"Помилка завантаження файлу для message_id={message_id}: {str(e)}")
		return None


def format_timestamp(timestamp: Optional[datetime]) -> Optional[datetime]:
	if timestamp:
		if timestamp.tzinfo:
			timestamp = timestamp.astimezone(timezone.utc).replace(tzinfo=None)
		return timestamp
	return datetime.now(timezone.utc).replace(tzinfo=None)


def format_message(
		room_id: str,
		account_id: str,
		user_id: str,
		user_name: str,
		content_text: Optional[str] = None,
		media_url: Optional[str] = None,
		message_id: str = None,
		timestamp: Optional[datetime] = None,
		reply_to_message_id: Optional[str] = None,
		social_media_channel: str = "telegram"
):
	timestamp = format_timestamp(timestamp)
	message = {
		"message_id": message_id or str(uuid.uuid4()),
		"message_time": timestamp.strftime('%Y-%m-%dT%H:%M:%S.%fZ') if timestamp else "",
		"room_id": room_id,
		"account_id": account_id,
		"time": timestamp.strftime('%Y-%m-%dT%H:%M:%S.%fZ') if timestamp else "",
		"user_id": user_id,
		"user_name": user_name or "UnknownUser",
		"social_media_channel": social_media_channel,
		"content": {
			"type": "text" if content_text else "media" if media_url else "unknown",
			"text": {"value": content_text} if content_text else None,
			"media": {"url": media_url} if media_url else None
		},
		"reply_to_message_id": reply_to_message_id
	}
	return message


@retry_db_operation
def load_seen_messages(seen_file: str):
	path = seen_file
	if os.path.exists(path):
		with open(path, 'r') as f:
			data = json.load(f)
			seen_messages = data.get("messages", {})
			last_processed = data.get("last_processed", {})
			valid_seen = {}
			for key in seen_messages:
				if frappe.db.exists("Message", {"message_id": key}):
					valid_seen[key] = seen_messages[key]
			return {"messages": valid_seen, "last_processed": last_processed, "_file": path}
	return {"messages": {}, "last_processed": {}, "_file": path}

@retry_db_operation
def save_seen_messages(seen_messages):
	path = seen_messages.get("_file")
	if not path:
		return
	backup_file = path + ".backup"
	if os.path.exists(path):
		with open(path, 'r') as f, open(backup_file, 'w') as bf:
			bf.write(f.read())
	with open(path, 'w') as f:
		json.dump(seen_messages, f)


@retry_db_operation
async def get_telegram_clients():
	clients = []
	site_name = get_site_name()
	# Отримання всіх агентів із доктайпа Agents, які не мають прапора disabled
	agents = frappe.get_list(
		"Agents",
		filters={"disabled": ["!=", 1]},
		fields=["name", "telegram_api_id", "telegram_api_hash", "telegram_phone", "llm_name"],
		ignore_permissions=True
	)

	# Use dynamically resolved TGCrewData as the root
	session_base_dir = os.path.join(DATA_ROOT, "sessions")
	os.makedirs(session_base_dir, exist_ok=True)

	# Мапінг імен агентів на імена сесій
	session_name_mapping = {
		"kattiecatlina": "bot1_session",
		"KatSweetie": "bot2_session"
	}

	for agent in agents:
		agent_name = agent.get("name")
		telegram_api_id = agent.get("telegram_api_id")
		telegram_api_hash = agent.get("telegram_api_hash")
		telegram_phone = agent.get("telegram_phone")
		llm_name = agent.get("llm_name") or agent_name

		# Перевірка наявності всіх необхідних полів
		if not all([agent_name, telegram_api_id, telegram_api_hash, telegram_phone]):
			logger.warning(f"Пропущено агента {agent_name}: відсутні параметри")
			continue

		# Перевірка коректності типу api_id
		try:
			telegram_api_id = int(telegram_api_id)
		except (ValueError, TypeError):
			logger.warning(f"Невірний тип telegram_api_id для агента {agent_name}: {telegram_api_id}")
			continue

		# Визначення імені сесії з мапінгу
		telegram_session_name = session_name_mapping.get(agent_name, f"{agent_name}_session")
		bot_root = get_bot_root(agent_name)
		os.makedirs(bot_root, exist_ok=True)
		# Store a single standard-named session file directly in bot root
		session_path = os.path.join(bot_root, "bot.session")

		session_exists = os.path.exists(session_path)
		logger.info(f"Сесія для агента {agent_name}: {'існує' if session_exists else 'не існує'} ({session_path})")

		# Створення конфігурації для агента
		config = {
			"name": agent_name,
			"telegram_session_name": telegram_session_name,
			"telegram_api_id": telegram_api_id,
			"telegram_api_hash": telegram_api_hash,
			"telegram_phone": telegram_phone,
			"llm_name": llm_name,
			"session_dir": bot_root,
			"seen_file": os.path.join(bot_root, "seen_messages.json")
		}

		# Запис конфігурації у JSON-файл
		config_file_path = os.path.join(bot_root, f"{telegram_session_name}_config.json")
		try:
			with open(config_file_path, 'w') as f:
				json.dump(config, f, indent=4, ensure_ascii=False)
			logger.info(f"Створено JSON-файл конфігурації для агента {agent_name}: {config_file_path}")
		except Exception as e:
			logger.error(f"Помилка запису JSON-файлу для агента {agent_name}: {str(e)}")
			continue

		# Ініціалізація клієнта Telegram
		client = TelegramClient(session_path, telegram_api_id, telegram_api_hash)

		try:
			await client.connect()
			if session_exists:
				if await client.is_user_authorized():
					logger.info(f"Сесія для агента {agent_name} валідна, повторна авторизація не потрібна")
				else:
					logger.warning(f"Сесія для агента {agent_name} не авторизована, потрібна авторизація")
					try:
						await client.start(phone=lambda: telegram_phone)
						logger.info(f"Сесія для агента {agent_name} успішно авторизована")
					except telethon.errors.PhoneNumberInvalidError:
						logger.error(f"Невірний номер телефону для агента {agent_name}: {telegram_phone}")
						continue
					except telethon.errors.SessionPasswordNeededError:
						logger.error(
							f"Потрібен пароль для агента {agent_name}. Двофакторна авторизація не підтримується.")
						continue
					except Exception as e:
						logger.error(f"Помилка авторизації для агента {agent_name}: {str(e)}")
						continue
			else:
				logger.info(f"Запуск авторизації для нового агента {agent_name}")
				try:
					await client.start(phone=lambda: telegram_phone)
					logger.info(f"Нова сесія створена для агента {agent_name}")
				except telethon.errors.PhoneNumberInvalidError:
					logger.error(f"Невірний номер телефону для агента {agent_name}: {telegram_phone}")
					continue
				except telethon.errors.SessionPasswordNeededError:
					logger.error(f"Потрібен пароль для агента {agent_name}. Двофакторна авторизація не підтримується.")
					continue
				except Exception as e:
					logger.error(f"Помилка створення нової сесії для агента {agent_name}: {str(e)}")
					continue
		except Exception as e:
			logger.error(f"Помилка підключення до Telegram для агента {agent_name}: {str(e)}")
			continue

		clients.append({'client': client, 'config': config})

	return clients


async def start_clients():
	global ACTIVE_CLIENTS, BOT_IDS, BOT_CHAT_IDS
	clients = await get_telegram_clients()
	for client_data in clients:
		client = client_data['client']
		config = client_data['config']
		logger.info(f"Ініціалізація клієнта: {config['name']}")
		try:
			# Перевірка, чи клієнт уже підключений і авторизований
			if not client.is_connected():
				await client.connect()
			is_connected = await client.is_user_authorized()
			if not is_connected:
				logger.error(f"Клієнт {config['name']} не авторизований")
				continue
			bot_id = str((await client.get_me()).id)
			BOT_IDS.add(bot_id)
			ACTIVE_CLIENTS[config['name']] = client
			BOT_CHAT_IDS[bot_id] = {}
			logger.info(f"Клієнт {config['name']} успішно ініціалізовано, bot_id: {bot_id}")
			await asyncio.sleep(2)
		except Exception as e:
			logger.error(f"Помилка ініціалізації клієнта {config['name']}: {str(e)}")
			continue
	return clients


@retry_db_operation
def create_or_update_room(chat_id: str, title: str, agent_name: str) -> Optional[str]:
	try:
		if not chat_id or not agent_name:
			logger.error("Невірний chat_id або agent_name надано для створення кімнати")
			return None

		room_id = f"{chat_id}:{agent_name}"
		existing_rooms = frappe.get_list(
			"Room",
			filters={"room_id": room_id},
			fields=["name", "title", "agent_name"],
			ignore_permissions=True
		)

		if existing_rooms:
			room = frappe.get_doc("Room", existing_rooms[0]["name"])
			if room.title != title and title and title != "Unnamed Room":
				room.title = title
				room.save(ignore_permissions=True)
				frappe.db.commit()
			return room.room_id

		room = frappe.new_doc("Room")
		room.room_id = room_id
		room.chat_id = chat_id
		room.sender_id = chat_id
		room.agent_name = agent_name
		room.title = title or f"Chat_{chat_id}_{agent_name}"
		room.insert(ignore_permissions=True, ignore_if_duplicate=True)
		frappe.db.commit()
		return room.room_id

	except frappe.DuplicateEntryError:
		existing_rooms = frappe.get_list(
			"Room",
			filters={"room_id": room_id},
			fields=["name", "room_id"],
			ignore_permissions=True
		)
		if existing_rooms:
			return existing_rooms[0]["room_id"]
		logger.error(f"Не вдалося знайти або створити кімнату для room_id {room_id}")
		return None
	except Exception as e:
		logger.error(f"Помилка створення/оновлення кімнати з room_id {room_id}: {str(e)}")
		return None


@retry_db_operation
def create_or_update_agent(user_id: str, agent_name: str) -> Optional[str]:
	try:
		existing_agent = frappe.get_list("Agents", filters={"user_id": user_id}, fields=["name", "agent_name"])
		if existing_agent:
			agent_doc = frappe.get_doc("Agents", existing_agent[0]["name"])
			if agent_doc.agent_name != agent_name:
				agent_doc.agent_name = agent_name
				agent_doc.title = agent_name
				agent_doc.save(ignore_permissions=True)
				frappe.db.commit()
			return agent_doc.name
		else:
			agent = frappe.new_doc("Agents")
			agent.user_id = user_id
			agent.agent_name = agent_name
			agent.title = agent_name
			agent.insert(ignore_permissions=True)
			frappe.db.commit()
			if frappe.db.exists("Agents", agent.name):
				return agent.name
			logger.error(f"Агент {agent.name} не знайдено після вставки")
			return None
	except Exception as e:
		logger.error(f"Помилка створення/оновлення агента з user_id {user_id}: {str(e)}")
		return None


@retry_db_operation
async def create_message(
		chat_id: str,
		room_id: Optional[str],
		message_id: str,
		sender_id: str,
		sender_name: str,
		text: Optional[str] = None,
		media_url: Optional[str] = None,
		agent_name: Optional[str] = None,
		media_type: Optional[str] = None,
		timestamp: Optional[datetime] = None,
		receiver_id: Optional[str] = None,
		client=None,
		is_response_template: bool = False,
		reply_to_message_id: Optional[str] = None
):
	log_data = {
		"action": "create_message",
		"chat_id": chat_id,
		"room_id": room_id,
		"message_id": message_id,
		"sender_id": sender_id,
		"sender_name": sender_name,
		"receiver_id": receiver_id,
		"agent_name": agent_name,
		"text": text,
		"media_url": media_url,
		"media_type": media_type,
		"timestamp": timestamp.isoformat() if timestamp else None,
		"status": "received" if receiver_id and not is_response_template else "sent",
		"is_response_template": is_response_template,
		"reply_to_message_id": reply_to_message_id,
		"stage": "start",
		"error": None
	}

	try:
		logger.info(f"Creating message with message_id: {message_id}, stage: start")

		if not hasattr(frappe.local, '_realtime_log'):
			frappe.local._realtime_log = []

		if not room_id and not is_response_template:
			log_data["stage"] = "error"
			log_data["error"] = "Room not found"
			logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
			return

		exists = frappe.db.exists("Message", {"message_id": message_id})
		if not is_response_template and exists:
			log_data["stage"] = "skipped"
			log_data["reason"] = "Message already exists"
			logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
			return

		formatted_message = format_message(
			room_id=room_id,
			account_id=sender_id,
			user_id=sender_id,
			user_name=sender_name,
			content_text=text,
			media_url=media_url,
			message_id=message_id,
			timestamp=timestamp,
			reply_to_message_id=reply_to_message_id,
			social_media_channel="telegram"
		)

		message = frappe.new_doc("Message")
		message.message_id = message_id
		message.room_id = room_id if not is_response_template else None
		message.sender_id = sender_id
		message.sender_name = sender_name or "UnknownUser"
		message.receiver = sender_id if receiver_id is None else receiver_id
		message.agent = agent_name  # if agent_name in ["kattiecatlina", "KatSweetie"] else "kattiecatlina"
		message.text = text or ""
		message.status = "received" if receiver_id and not is_response_template else "sent"
		message.is_response_template = is_response_template
		message.reply_to_message_id = reply_to_message_id

		if media_url and media_type:
			message.media_url = media_url
			if media_type == "image":
				html_content = f'<img src="{media_url}" alt="Media" style="max-width: 197px; max-height: 141px; object-fit: contain;">'
			elif media_type == "voice":
				html_content = f'<audio controls><source src="{media_url}" type="audio/ogg">Your browser does not support the audio element.</audio>'
			else:
				html_content = f'<a href="{media_url}" target="_blank">Media Link</a>'
			message.media_html = html_content
		else:
			if text == "Media unavailable (FileReferenceExpiredError)":
				message.media_html = "<p>Media unavailable due to download error</p>"
			else:
				message.media_html = None
			message.media_url = None

		log_data["stage"] = "before_insert"
		log_data["message_summary"] = {
			"message_id": formatted_message.get("message_id"),
			"room_id": formatted_message.get("room_id"),
			"user_name": formatted_message.get("user_name"),
			"content_type": formatted_message.get("content", {}).get("type"),
			"has_text": bool(formatted_message.get("content", {}).get("text")),
			"has_media": bool(formatted_message.get("content", {}).get("media"))
		}
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		message.insert(ignore_permissions=True)
		logger.info(f"Message inserted with message_id: {message_id}")
		frappe.db.commit()
		logger.info(f"Database commit successful for message_id: {message_id}")

		log_data["stage"] = "success"
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		with open('messages.json', 'a') as f:
			json.dump([formatted_message], f, ensure_ascii=False)
			f.write('\n')

		frappe.publish_realtime(
			event="new_message",
			message=formatted_message,
			user=None,
			room=room_id
		)

		return message

	except Exception as e:
		log_data["stage"] = "error"
		log_data["error"] = str(e)
		log_data["traceback"] = traceback.format_exc()
		logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
		return None


async def try_download_media(client, message, message_id: str, max_attempts: int = 3):
	temp_file = f"temp_media_{message_id}_{random.randint(1, 10000)}.bin"
	media_type = None
	file_name = None
	mime_type = None

	try:
		if not hasattr(message, 'media') or message.media is None:
			return None, None, temp_file

		if hasattr(message, 'media') and hasattr(message.media,
												 'ttl_seconds') and message.media.ttl_seconds is not None:
			logger.warning(
				f"Повідомлення {message_id} містить медіа, що самознищується, з TTL {message.media.ttl_seconds}")
			return None, None, temp_file

		if isinstance(message.media, types.MessageMediaPhoto):
			media_type = "image"
			mime_type = getattr(message.media.photo, 'mime_type', 'image/jpeg')
			extension = mimetypes.guess_extension(mime_type) or '.bin'
			file_name = f"photo_{message_id}{extension}"
		elif isinstance(message.media, types.MessageMediaDocument):
			doc = message.media.document
			if isinstance(doc, DocumentEmpty):
				return None, None, temp_file
			mime_type = getattr(doc, 'mime_type', None)
			is_voice = any(
				isinstance(attr, DocumentAttributeAudio) and attr.voice for attr in getattr(doc, 'attributes', []))
			if is_voice and mime_type and mime_type.startswith('audio/'):
				media_type = "voice"
				file_name = f"voice_{message_id}.ogg"
				mime_type = "audio/ogg"
			elif mime_type and mime_type.startswith('image/'):
				media_type = "image"
				file_name = next(
					(attr.file_name for attr in getattr(doc, 'attributes', []) if
					 isinstance(attr, DocumentAttributeFilename)),
					None
				)
				extension = mimetypes.guess_extension(mime_type) or '.bin'
				file_name = file_name or f"image_{message_id}{extension}"
			else:
				media_type = "document"
				file_name = next(
					(attr.file_name for attr in getattr(doc, 'attributes', []) if
					 isinstance(attr, DocumentAttributeFilename)),
					None
				)
				extension = mimetypes.guess_extension(mime_type or "application/octet-stream") or '.bin'
				file_name = file_name or f"document_{message_id}{extension}"

		for attempt in range(1, max_attempts + 1):
			try:
				media_path = await client.download_media(message, file=temp_file)
				if media_path:
					file_url = upload_to_frappe(media_path, file_name, message_id)
					if file_url:
						return file_url, media_type, temp_file
					else:
						logger.error(f"Завантаження медіа до Frappe не вдалося для message_id: {message_id}")
						return None, None, temp_file
				else:
					logger.error(f"Завантаження медіа повернуло порожній шлях для message_id: {message_id}")
					return None, None, temp_file
			except FileReferenceExpiredError:
				logger.warning(
					f"Термін дії посилання на файл закінчився для message_id: {message_id} на спробі {attempt}")
				if attempt == max_attempts:
					logger.error(
						f"Не вдалося завантажити медіа після {max_attempts} спроб для message_id: {message_id}")
					return None, None, temp_file
				await asyncio.sleep(1)
			except Exception as e:
				logger.error(f"Помилка завантаження медіа для message_id: {message_id} на спробі {attempt}: {str(e)}")
				if attempt == max_attempts:
					logger.error(
						f"Не вдалося завантажити медіа після {max_attempts} спроб для message_id: {message_id}")
					return None, None, temp_file
		return None, None, temp_file

	except Exception as e:
		logger.error(f"Непередбачена помилка в try_download_media для message_id: {message_id}: {str(e)}")
		return None, None, temp_file

	finally:
		if os.path.exists(temp_file):
			try:
				os.remove(temp_file)
			except Exception as e:
				logger.error(f"Помилка видалення тимчасового файлу {temp_file}: {str(e)}")


@retry_db_operation
async def get_pending_response_templates(agent_name: str) -> list:
	log_data = {
		"action": "get_pending_response_templates",
		"agent_name": agent_name,
		"stage": "start",
		"error": None
	}
	try:
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
		logger.info(f"agent_name: {agent_name}")

		response_templates = frappe.get_list(
			"Message",
			filters={
				"agent": agent_name,
				"is_response_template": 1,
				"status": "template"
			},
			fields=["name", "text", "creation", "room_id"],
			order_by="creation asc",
			ignore_permissions=True
		)
		frappe.db.commit()

		for template in response_templates:
			if isinstance(template.get("creation"), datetime):
				template["creation"] = template["creation"].isoformat()

		log_data["stage"] = "templates_fetched"
		log_data["template_count"] = len(response_templates)
		log_data["template_names"] = [t.get("name") for t in response_templates[:5]]  # Only show first 5 names
		if len(response_templates) > 5:
			log_data["template_names"].append(f"... and {len(response_templates) - 5} more")
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		frappe.db.commit()

		# if not response_templates:
		# 	logger.warning(
		# 		f"Не знайдено шаблонних повідомлень для агента {agent_name}. Перевірте фільтри: agent={agent_name}, is_response_template=1, status=template")
		return response_templates

	except Exception as e:
		log_data["stage"] = "error"
		log_data["error"] = str(e)
		log_data["traceback"] = traceback.format_exc()
		logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
		return []

@retry_db_operation
async def send_telegram_message(chat_id: str, agent_name: str, message_name: str):
	log_data = {
		"action": "send_telegram_message",
		"chat_id": chat_id,
		"agent_name": agent_name,
		"message_name": message_name,
		"stage": "start",
		"error": None
	}
	try:
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		if not chat_id:
			log_data["stage"] = "error"
			log_data["error"] = f"chat_id не надано для відправки повідомлення"
			logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
			return False, None

		client = ACTIVE_CLIENTS.get(agent_name)
		if not client:
			log_data["stage"] = "error"
			log_data["error"] = f"Немає активного клієнта для агента {agent_name}"
			logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
			return False, None

		message_doc = frappe.get_doc("Message", message_name)
		if not message_doc.is_response_template:
			log_data["stage"] = "error"
			log_data["error"] = f"Повідомлення {message_name} не є шаблоном відповіді"
			logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
			return False, None

		logger.info(f"Спроба відправки повідомлення {message_name} до chat_id {chat_id}")
		entity = await client.get_entity(int(chat_id))
		me = await client.get_me()
		await client.send_read_acknowledge(entity)

		async def _send():
			await client(functions.messages.SetTypingRequest(peer=entity, action=types.SendMessageTypingAction()))
			await asyncio.sleep(random.uniform(5, 15))
			if message_doc.media_url:
				sent_message = await client.send_file(entity, file=message_doc.media_url, caption=message_doc.text or "")
			else:
				sent_message = await client.send_message(entity, message_doc.text or "")
			telegram_message_id = str(sent_message.id)
			log_data["stage"] = "message_sent"
			log_data["sent_message"] = {
				"message_id": telegram_message_id,
				"text": message_doc.text,
				"media_url": message_doc.media_url,
				"timestamp": sent_message.date.isoformat() if sent_message.date else None
			}
			logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
			message_doc.status = "sent"
			message_doc.message_id = telegram_message_id
			message_doc.sender_id = str(me.id)
			message_doc.sender_name = agent_name
			message_doc.save(ignore_permissions=True)
			frappe.db.commit()
			room_id = message_doc.room_id or create_or_update_room(chat_id, entity.username or entity.first_name or f"User_{chat_id}", agent_name)
			if not room_id:
				return False, None
			await create_message(
				chat_id=chat_id,
				room_id=room_id,
				message_id=telegram_message_id,
				sender_id=str(me.id),
				sender_name=agent_name,
				text=message_doc.text,
				media_url=message_doc.media_url,
				agent_name=agent_name,
				media_type="image" if message_doc.media_url else None,
				timestamp=sent_message.date,
				receiver_id=chat_id,
				client=client,
				reply_to_message_id=None
			)
			log_data["stage"] = "message_saved"
			log_data["message_id"] = telegram_message_id
			logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
			logger.info(f"Вихідне повідомлення відправлено: message_id={telegram_message_id}, room_id={room_id}, text={message_doc.text}, media_url={message_doc.media_url}, sender_id={str(me.id)}, timestamp={sent_message.date}")

		asyncio.create_task(_send())
		return True, None

	except telethon.errors.ChatWriteForbiddenError:
		log_data["stage"] = "error"
		log_data["error"] = f"Бот {agent_name} не має прав на відправку повідомлень у chat_id {chat_id}"
		logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
		return False, None
	except Exception as e:
		log_data["stage"] = "error"
		log_data["error"] = str(e)
		log_data["traceback"] = traceback.format_exc()
		logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
		return False, None


async def process_pending_messages(client, client_name: str):
	while True:
		try:
			templates = await get_pending_response_templates(client_name)
			if not templates:
				logger.debug(f"Немає шаблонних повідомлень для відправки для агента {client_name}")
			else:
				logger.info(f"Знайдено {len(templates)} шаблонних повідомлень для відправки для агента {client_name}")
			for template in templates:
				message_doc = frappe.get_doc("Message", template["name"])
				room_id = template.get("room_id")
				if not room_id:
					logger.warning(f"Пропущено повідомлення {template['name']}: room_id не вказано")
					continue

				chat_id = room_id.split(":")[0] if ":" in room_id else None
				if not chat_id:
					logger.warning(
						f"Пропущено повідомлення {template['name']}: не вдалося отримати chat_id з room_id {room_id}")
					continue

				success, telegram_message_id = await send_telegram_message(chat_id, client_name, template["name"])
				if success:
					logger.info(f"Повідомлення {template['name']} успішно відправлено з ID {telegram_message_id}")
				else:
					logger.error(f"Не вдалося відправити повідомлення {template['name']}")
				await asyncio.sleep(2)
		except Exception as e:
			logger.error(f"Помилка обробки неотправлених повідомлень для {client_name}: {str(e)}")
		await asyncio.sleep(30)


async def check_new_messages(client, client_name: str, seen_messages: dict, config: dict):
	while True:
		try:
			logger.debug(f"PERIODIC MESSAGE CHECK STARTING for agent {client_name}")
			bot_id = str((await client.get_me()).id)
			dialogs = await client.get_dialogs()
			time_threshold = datetime.now(timezone.utc) - timedelta(minutes=30)
			logger.debug(f"PERIODIC CHECK: Found {len(dialogs)} dialogs, checking messages newer than {time_threshold}")

			new_messages_found = 0

			for dialog in dialogs:
				entity = dialog.entity
				if not isinstance(entity, types.User):
					continue
				chat_id = str(entity.id)

				sender_id = chat_id
				agent_count = sum(1 for bot_ids in BOT_CHAT_IDS.values() for agent_chat_id in bot_ids if
								  agent_chat_id.startswith(sender_id))
				if agent_count > 1:
					chat_id = f"{sender_id}:{client_name}"

				last_processed = seen_messages["last_processed"].get(chat_id)
				last_processed_time = datetime.fromisoformat(
					last_processed.replace('Z', '+00:00')) if last_processed else None

				async for message in client.iter_messages(entity, limit=5):
					message_date = message.date.replace(tzinfo=timezone.utc) if message.date else datetime.now(
						timezone.utc)

					logger.debug(f"PERIODIC CHECK FOUND MESSAGE: message_id={message.id}, chat_id={chat_id}, sender_id={message.sender_id}")

					if str(message.sender_id) in BOT_IDS:
						logger.debug(f"SKIPPING MESSAGE FROM BOT: message_id={message.id}")
						continue
					if message_date < time_threshold:
						logger.debug(f"SKIPPING OLD MESSAGE: message_id={message.id}")
						continue
					if frappe.db.exists("Message", {"message_id": str(message.id)}):
						logger.debug(f"SKIPPING EXISTING MESSAGE: message_id={message.id}")
						seen_messages["messages"][str(message.id)] = True
						save_seen_messages(seen_messages)
						continue
					if last_processed_time and message_date <= last_processed_time:
						logger.debug(f"SKIPPING ALREADY PROCESSED MESSAGE: message_id={message.id}")
						continue

					logger.info(f"PROCESSING NEW MESSAGE FROM PERIODIC CHECK: message_id={message.id}, text='{message.text}'")
					new_messages_found += 1
					await process_message(message, client_name, client, seen_messages, config, is_historical=False)
					await asyncio.sleep(0.1)

			if new_messages_found > 0:
				logger.info(f"PERIODIC CHECK COMPLETED: Found {new_messages_found} new messages for agent {client_name}")
			else:
				logger.debug(f"PERIODIC CHECK COMPLETED: No new messages for agent {client_name}")
		except Exception as e:
			logger.error(f"Помилка перевірки нових повідомлень для {client_name}: {str(e)}")
		await asyncio.sleep(30)


async def process_message(message, client_name: str, client, seen_messages: dict, config: dict,
						  is_historical: bool = False):
	message_id = str(message.id)
	chat_id = str(message.chat_id)
	date = message.date.replace(tzinfo=timezone.utc) if message.date else datetime.now(timezone.utc)

	logger.info(f"PROCESS_MESSAGE ENTRY: message_id={message_id}, chat_id={chat_id}, sender_id={message.sender_id}, text='{message.text}', agent={client_name}, is_historical={is_historical}, has_media={bool(message.media)}, date={date}")

	log_data = {
		"action": "process_message",
		"message_id": message_id,
		"chat_id": chat_id,
		"client_name": client_name,
		"is_historical": is_historical,
		"stage": "start",
		"error": None
	}

	try:
		logger.info(f"Processing message with message_id: {message_id}, stage: start")
		if chat_id == "777000":
			# "Повідомлення від офіційного Telegram-акаунта"
			return

		if frappe.db.exists("Message", {"message_id": message_id, "agent": client_name, "sender_id": message.sender_id}):
			log_data["stage"] = "skipped"
			log_data["reason"] = "Message already exists in database"
			logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
			return

		bot_id = str((await client.get_me()).id)
		sender_id = str(message.sender_id) if message.sender_id else chat_id
		if sender_id in BOT_IDS and not is_historical:
			log_data["stage"] = "skipped"
			log_data["reason"] = "Message from bot"
			logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))
			return

		if chat_id not in BOT_CHAT_IDS.get(bot_id, {}) and not is_historical:
			BOT_CHAT_IDS[bot_id][chat_id] = client_name
			logger.info(f"Прив’язано chat_id {chat_id} до агента {client_name}")

		media_url = None
		media_type = None
		temp_file = None

		message_details = {
			"message_id": message_id,
			"chat_id": chat_id,
			"sender_id": sender_id,
			"text": message.text or "",
			"date": date.isoformat(),
			"is_historical": is_historical,
			"client_name": client_name,
			"has_media": bool(message.media),
			"media_type": None,
			"media_mime_type": None,
			"media_ttl_seconds": getattr(message.media, 'ttl_seconds', None) if message.media else None,
			"sender": {
				"username": None,
				"first_name": None,
				"last_name": None,
				"phone": None,
				"is_bot": None,
				"is_premium": None
			},
			"message": {
				"is_outgoing": message.out,
				"is_reply": message.is_reply,
				"reply_to_msg_id": message.reply_to_msg_id,
				"mentioned": message.mentioned,
				"silent": message.silent,
				"post": message.post,
				"from_scheduled": message.from_scheduled,
				"edit_date": message.edit_date.isoformat() if message.edit_date else None,
				"forward": {
					"from_id": str(message.forward.from_id) if message.forward and message.forward.from_id else None,
					"from_name": message.forward.from_name if message.forward else None,
					"date": message.forward.date.isoformat() if message.forward and message.forward.date else None
				}
			}
		}

		if message.media:
			media_url, media_type, temp_file = await try_download_media(client, message, message_id)
			message_details["media_type"] = media_type
			message_details["media_mime_type"] = getattr(message.media, 'mime_type', None) if isinstance(message.media,
																										 (types.MessageMediaPhoto,
																										  types.MessageMediaDocument)) else None
			message_details["media_url"] = media_url

		sender = await message.get_sender()
		if sender:
			message_details["sender"]["username"] = sender.username
			message_details["sender"]["first_name"] = sender.first_name
			message_details["sender"]["last_name"] = sender.last_name
			message_details["sender"]["phone"] = sender.phone
			message_details["sender"]["is_bot"] = sender.bot
			message_details["sender"]["is_premium"] = sender.premium

		log_data["stage"] = "message_received"
		log_data["message_summary"] = {
			"message_id": message_details["message_id"],
			"chat_id": message_details["chat_id"],
			"sender_id": message_details["sender_id"],
			"text_length": len(message_details["text"]),
			"has_media": message_details["has_media"],
			"media_type": message_details["media_type"],
			"sender_username": message_details["sender"]["username"]
		}
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		sender_name = (sender.username or sender.first_name or "UnknownUser") if sender else "UnknownUser"
		chat_title = sender_name or f"User_{sender_id}"

		room_id = create_or_update_room(chat_id, chat_title, client_name)
		if not room_id:
			log_data["stage"] = "error"
			log_data["error"] = f"Failed to create or update room for chat_id {chat_id}"
			logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))
			return

		log_data["stage"] = "room_created"
		log_data["room_id"] = room_id
		log_data["chat_title"] = chat_title
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		receiver_id = bot_id if not is_historical else None
		await create_message(
			chat_id=chat_id,
			room_id=room_id,
			message_id=message_id,
			sender_id=sender_id,
			sender_name=sender_name,
			text=message.text or "",
			media_url=media_url,
			agent_name=client_name,
			media_type=media_type,
			timestamp=date,
			receiver_id=receiver_id,
			client=client
		)

		seen_messages["messages"][message_id] = True
		seen_messages["last_processed"][chat_id] = date.isoformat()
		save_seen_messages(seen_messages)

		log_data["stage"] = "message_saved"
		logger.info(json.dumps(log_data, indent=2, ensure_ascii=False))

		if temp_file and os.path.exists(temp_file):
			try:
				os.remove(temp_file)
			except Exception as e:
				logger.error(f"Помилка видалення тимчасового файлу {temp_file}: {str(e)}")

	except Exception as e:
		log_data["stage"] = "error"
		log_data["error"] = str(e)
		log_data["traceback"] = traceback.format_exc()
		logger.error(json.dumps(log_data, indent=2, ensure_ascii=False))


async def listen_messages(clients: list):
	if not clients:
		logger.error("Немає налаштованих клієнтів Telegram")
		return

	tasks = []
	for client_data in clients:
		client = client_data['client']
		config = client_data['config']
		try:
			bot_id = str((await client.get_me()).id)
			create_or_update_agent(bot_id, config['name'])
			seen = load_seen_messages(config.get('seen_file'))
			os.makedirs(os.path.dirname(config.get('seen_file')), exist_ok=True)
			tasks.append(check_new_messages(client, config['name'], seen, config))
			tasks.append(process_pending_messages(client, config['name']))

			logger.info(f"SETTING UP EVENT HANDLER for agent {config['name']} with bot_id {bot_id}")
			@client.on(events.NewMessage(incoming=True))
			async def handler(event):
				try:
					message = event.message
					chat_id = str(message.chat_id)
					bot_id = str((await client.get_me()).id)

					logger.info(f"NEW MESSAGE RECEIVED - Event Handler: message_id={message.id}, chat_id={chat_id}, sender_id={message.sender_id}, text='{message.text}', agent={config['name']}, has_media={bool(message.media)}")

					sender_id = str(message.sender_id) if message.sender_id else chat_id
					agent_count = sum(1 for bot_ids in BOT_CHAT_IDS.values() for agent_chat_id in bot_ids if
									  agent_chat_id.startswith(sender_id))
					if agent_count > 1:
						chat_id = f"{sender_id}:{config['name']}"

					current_agent = BOT_CHAT_IDS.get(bot_id, {}).get(chat_id)
					if current_agent and current_agent != config['name']:
						logger.info(
							f"Пропущено повідомлення для chat_id {chat_id}: прив’язаний до агента {current_agent}")
						return

					if chat_id not in BOT_CHAT_IDS.get(bot_id, {}):
						BOT_CHAT_IDS[bot_id][chat_id] = config['name']
						logger.info(f"Прив’язано chat_id {chat_id} до агента {config['name']}")

					if frappe.db.exists("Message", {"message_id": str(message.id)}):
						return

					await process_message(event.message, config['name'], client, seen, config,
										  is_historical=False)
				except Exception as e:
					logger.error(f"Помилка обробки нового повідомлення для {config['name']}: {str(e)}")
		except Exception as e:
			logger.error(f"Помилка ініціалізації клієнта {config['name']}: {str(e)}")
			continue

	try:
		await asyncio.gather(*tasks, return_exceptions=True)
	except Exception as e:
		logger.error(f"Помилка виконання завдань: {str(e)}")

async def main():
	try:
		clients = await start_clients()
		await listen_messages(clients)
	except Exception as e:
		logger.error(f"Критична помилка в main: {str(e)}")


def run_telegram_bot():
	logger.info("Запуск бота Telegram")
	try:
		asyncio.run(main())
	except KeyboardInterrupt:
		logger.info("Отримано KeyboardInterrupt, збереження seen_messages та зупинка")
	except Exception as e:
		logger.error(f"Помилка роботи бота: {str(e)}")


if __name__ == "__main__":
	run_telegram_bot()
